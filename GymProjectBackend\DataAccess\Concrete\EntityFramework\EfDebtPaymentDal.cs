using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfDebtPaymentDal : EfCompanyEntityRepositoryBase<DebtPayment, GymContext>, IDebtPaymentDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public EfDebtPaymentDal(GymContext context, Core.Utilities.Security.CompanyContext.ICompanyContext companyContext) : base(context, companyContext)
        {
            _companyContext = companyContext;
        }

        public IResult DeleteDebtPaymentWithRemainingDebtUpdate(int debtPaymentId)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    using (var context = new GymContext())
                    {
                        // DebtPayment kaydını bul
                        var debtPayment = context.DebtPayments.FirstOrDefault(dp => dp.DebtPaymentID == debtPaymentId);
                        if (debtPayment == null)
                            return new ErrorResult("Borç ödemesi bulunamadı.");

                        // İlgili RemainingDebt kaydını bul
                        var remainingDebt = context.RemainingDebts.FirstOrDefault(rd => rd.RemainingDebtID == debtPayment.RemainingDebtID);
                        if (remainingDebt == null)
                            return new ErrorResult("Kalan borç kaydı bulunamadı.");

                        // Kalan borç tutarını güncelle (silinen ödeme miktarını geri ekle)
                        remainingDebt.RemainingAmount += debtPayment.PaidAmount;
                        remainingDebt.LastUpdateDate = DateTime.Now;

                        // DebtPayment kaydını sil
                        context.DebtPayments.Remove(debtPayment);

                        // Değişiklikleri kaydet
                        context.SaveChanges();

                        scope.Complete();
                        return new SuccessResult("Borç ödemesi başarıyla silindi.");
                    }
                }
                catch (Exception ex)
                {
                    scope.Dispose();
                    return new ErrorResult($"Borç ödemesi silinirken bir hata oluştu: {ex.Message}");
                }
            }
        }
    }
}
